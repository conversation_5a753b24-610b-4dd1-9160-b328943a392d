services:
  # FastAPI application (production)
  mcp:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "8080:${MCP_PORT}"
    environment:
      - MCP_SERVER_NAME=${MCP_SERVER_NAME:-SearXNG MCP Server}
      - MCP_SERVER_VERSION=${MCP_SERVER_VERSION:-0.1.0}
      - SEARXNG_URL=${SEARXNG_URL:-http://localhost:8080}
      - MCP_SEARCH_LIMIT=${MCP_SEARCH_LIMIT:-10}
      - MCP_SEARCH_TIMEOUT=${MCP_SEARCH_TIMEOUT:-30}
      - MCP_TRANSPORT=${MCP_TRANSPORT:-http}
      - MCP_HOST=${MCP_HOST}
      - MCP_PORT=${MCP_PORT}
      - MCP_PATH=${MCP_PATH}
      - MCP_DEBUG=${MCP_DEBUG}
      - MCP_LOG_LEVEL=${MCP_LOG_LEVEL}
      - MCP_AUTH_ENABLED=${MCP_AUTH_ENABLED}
    networks:
      - mcp-prod-network
    restart: unless-stopped

networks:
  mcp-prod-network:
    driver: bridge
