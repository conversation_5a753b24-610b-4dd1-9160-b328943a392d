services:
  # FastAPI application (production)
  mcp:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "8080:8000"
    environment:
      - MCP_SERVER_NAME=${MCP_SERVER_NAME:-FastMCP Template Server}
      - MCP_SERVER_VERSION=${MCP_SERVER_VERSION:-0.1.0}
      - MCP_TRANSPORT=${MCP_TRANSPORT:-http}
      - MCP_HOST=${MCP_HOST}
      - MCP_PORT=${MCP_PORT}
      - MCP_PATH=${MCP_PATH}
      - MCP_DEBUG=${MCP_DEBUG}
      - MCP_LOG_LEVEL=${MCP_LOG_LEVEL}
      - MCP_AUTH_ENABLED=${MCP_AUTH_ENABLED}
      - MCP_API_KEY=${MCP_API_KEY:-}
    networks:
      - mcp-prod-network
    restart: unless-stopped

networks:
  mcp-prod-network:
    driver: bridge
