# FastMCP Server Configuration
# Copy this file to .env and customize the values

# Server Information
MCP_SERVER_NAME="FastMCP Searxng Server"
MCP_SERVER_VERSION="0.1.0"
MCP_DEBUG=false

# Transport Configuration
# Options: stdio, http, sse
MCP_TRANSPORT=http
MCP_HOST=0.0.0.0
MCP_PORT=8000
MCP_PATH=/mcp

# Logging
# Options: debug, info, warning, error, critical
MCP_LOG_LEVEL=info

# Authentication (for HTTP/SSE transports)
MCP_AUTH_ENABLED=false
MCP_AUTH_SECRET=dev-secret-key-for-development-only-change-in-production

# Example API Configuration
MCP_API_KEY=your-api-key-here
MCP_API_BASE_URL=http://api.example.com
