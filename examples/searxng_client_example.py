#!/usr/bin/env python3
"""
Example client for the SearXNG MCP server.

This example demonstrates how to use the SearXNG MCP server to perform
web searches using the FastMCP client library.
"""

import asyncio
import json
from fastmcp import Client
from app.main import mcp


async def main():
    """Demonstrate SearXNG MCP server capabilities."""
    print("🔍 SearXNG MCP Server Example")
    print("=" * 50)
    
    async with Client(mcp) as client:
        # Test server connection
        print("\n1. Testing server connection...")
        try:
            await client.ping()
            print("✅ Server connection successful")
        except Exception as e:
            print(f"❌ Server connection failed: {e}")
            return
        
        # List available tools
        print("\n2. Available tools:")
        tools = await client.list_tools()
        for tool in tools:
            print(f"   • {tool.name}: {tool.description}")
        
        # Test connection to SearXNG (this will fail without a real instance)
        print("\n3. Testing SearXNG connection...")
        try:
            result = await client.call_tool("test_connection", {})
            print(f"   {result.data}")
        except Exception as e:
            print(f"   ❌ Connection test failed: {e}")
        
        # Demonstrate search (with mock data since no real SearXNG instance)
        print("\n4. Example search (simulated):")
        print("   Note: This would normally connect to a SearXNG instance")
        print("   Query: 'artificial intelligence latest developments'")
        print("   Expected output:")
        print("""
   Search Query: artificial intelligence latest developments
   Total Results: 10
   
   === SEARCH RESULTS ===
   1. Latest AI Breakthroughs in 2024
      URL: https://example.com/ai-2024
      Content: Recent developments in artificial intelligence including...
   
   2. OpenAI Announces New Model
      URL: https://openai.com/news
      Content: OpenAI has released a new language model with improved...
        """)
        
        # Show available resources
        print("\n5. Available resources:")
        resources = await client.list_resources()
        for resource in resources:
            print(f"   • {resource.uri}")
        
        # Read configuration
        print("\n6. Server configuration:")
        config_resource = await client.read_resource("config://searxng")
        config_data = json.loads(config_resource[0].text)
        print(f"   • Server: {config_data['server_name']}")
        print(f"   • Version: {config_data['server_version']}")
        print(f"   • SearXNG URL: {config_data['searxng_url']}")
        print(f"   • Search limit: {config_data['search_limit']}")
        
        # Show help information
        print("\n7. Search syntax help:")
        help_resource = await client.read_resource("help://search/syntax")
        print(help_resource[0].text[:200] + "...")
        
        # List available prompts
        print("\n8. Available prompts:")
        prompts = await client.list_prompts()
        for prompt in prompts:
            print(f"   • {prompt.name}: {prompt.description}")
        
        # Generate a search prompt
        print("\n9. Example search prompt:")
        search_prompt = await client.get_prompt(
            "search_prompt",
            {
                "query": "climate change solutions",
                "context": "research for environmental policy paper"
            }
        )
        print(f"   {search_prompt}")
        
        print("\n" + "=" * 50)
        print("🎉 SearXNG MCP Server example completed!")
        print("\nTo use with a real SearXNG instance:")
        print("1. Set up SearXNG: https://docs.searxng.org/")
        print("2. Set environment variable: export MCP_SEARXNG_URL='http://your-searxng-url'")
        print("3. Run the server: uv run mcp-searxng")


if __name__ == "__main__":
    asyncio.run(main())
