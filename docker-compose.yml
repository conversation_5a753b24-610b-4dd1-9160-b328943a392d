services:
  # FastMCP server (development)
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:${MCP_PORT}"
    volumes:
      - .:/app
      - /app/.venv  # Exclude virtual environment from volume mount
    environment:
      - MCP_SERVER_NAME=${MCP_SERVER_NAME:-FastMCP Template Server}
      - MCP_SERVER_VERSION=${MCP_SERVER_VERSION:-0.1.0}
      - MCP_TRANSPORT=${MCP_TRANSPORT:-http}
      - MCP_HOST=${MCP_HOST}
      - MCP_PORT=${MCP_PORT}
      - MCP_PATH=${MCP_PATH}
      - MCP_DEBUG=${MCP_DEBUG}
      - MCP_LOG_LEVEL=${MCP_LOG_LEVEL}
      - MCP_AUTH_ENABLED=${MCP_AUTH_ENABLED}
      - MCP_API_KEY=${MCP_API_KEY:-}
    networks:
      - mcp-dev-network
    restart: unless-stopped

networks:
  mcp-dev-network:
    driver: bridge
