"""FastMCP server template with comprehensive examples."""

import logging
from typing import Any

from fastmcp import FastMCP

from .config import Settings
from .prompts import register_prompts
from .resources import register_resources
from .tools import register_tools

# Initialize settings
settings = Settings()

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create FastMCP server instance
mcp: Any = FastMCP(name=settings.server_name, version=settings.server_version)

# Register tools, resources, and prompts from separate modules
register_tools(mcp)
register_resources(mcp)
register_prompts(mcp)


# ============================================================================
# SERVER STARTUP AND CONFIGURATION
# ============================================================================


def main() -> None:
    """Main entry point for the MCP server."""
    logger.info(f"Starting {settings.server_name} v{settings.server_version}")
    logger.info(f"Transport: {settings.transport}")

    if settings.transport == "stdio":
        # STDIO transport - default for local usage
        mcp.run(transport="stdio")
    elif settings.transport == "http":
        # Streamable HTTP transport - recommended for web deployments
        logger.info(
            f"Starting HTTP server on {settings.host}:{settings.port}{settings.path}"
        )
        mcp.run(
            transport="http",
            host=settings.host,
            port=settings.port,
            path=settings.path,
            log_level=settings.log_level,
        )
    elif settings.transport == "sse":
        # SSE transport - legacy, prefer HTTP for new deployments
        logger.info(f"Starting SSE server on {settings.host}:{settings.port}")
        mcp.run(
            transport="sse",
            host=settings.host,
            port=settings.port,
            log_level=settings.log_level,
        )
    else:
        raise ValueError(f"Unsupported transport: {settings.transport}")


def run_dev() -> None:
    """Run server in development mode with auto-reload."""
    # Override settings for development
    dev_settings = Settings(
        debug=True,
        log_level="debug",
        reload=True,
        transport="http",  # Use HTTP for development
    )

    logger.info("Starting development server")
    mcp.run(
        transport="http",
        host=dev_settings.host,
        port=dev_settings.port,
        path=dev_settings.path,
        log_level="debug",
    )


def run_prod() -> None:
    """Run server in production mode."""
    # Override settings for production
    prod_settings = Settings(
        debug=False,
        log_level="info",
        reload=False,
        transport="http",
        host="0.0.0.0",  # Bind to all interfaces in production
    )

    logger.info("Starting production server")
    mcp.run(
        transport="http",
        host=prod_settings.host,
        port=prod_settings.port,
        path=prod_settings.path,
        log_level="info",
    )


if __name__ == "__main__":
    main()
