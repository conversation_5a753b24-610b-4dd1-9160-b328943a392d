"""FastMCP prompts - Reusable message templates for LLM interactions."""

import logging
from typing import Any

logger = logging.getLogger(__name__)


def register_prompts(mcp: Any) -> None:
    """Register all prompts with the FastMCP server instance."""

    @mcp.prompt
    def analyze_data_prompt(
        data_description: str, analysis_type: str = "summary"
    ) -> str:
        """Generate a prompt for data analysis.

        Args:
            data_description: Description of the data to analyze
            analysis_type: Type of analysis (summary, trends, insights, statistical)
        """
        prompts = {
            "summary": f"Please provide a concise summary of the following data: {data_description}",
            "trends": f"Analyze the trends and patterns in this data: {data_description}",
            "insights": f"What insights and actionable recommendations can you derive from: {data_description}",
            "statistical": f"Perform a statistical analysis of this data and highlight key metrics: {data_description}",
        }

        return prompts.get(analysis_type, prompts["summary"])

    @mcp.prompt
    def code_review_prompt(code: str, language: str = "python") -> str:
        """Generate a prompt for code review.

        Args:
            code: Code to review
            language: Programming language
        """
        return f"""Please review the following {language} code and provide feedback on:

1. **Code Quality & Best Practices**
   - Adherence to {language} conventions and style guidelines
   - Code readability and maintainability
   - Proper use of language features

2. **Potential Issues**
   - Bugs or logical errors
   - Security vulnerabilities
   - Edge cases not handled

3. **Performance Considerations**
   - Efficiency of algorithms and data structures
   - Memory usage optimization
   - Potential bottlenecks

4. **Suggestions for Improvement**
   - Refactoring opportunities
   - Alternative approaches
   - Additional features or error handling

Code to review:
```{language}
{code}
```

Please provide specific, actionable feedback with examples where appropriate."""

    @mcp.prompt
    def explain_concept_prompt(concept: str, audience: str = "general") -> str:
        """Generate a prompt to explain a concept to a specific audience.

        Args:
            concept: Concept to explain
            audience: Target audience (general, technical, beginner, expert, child)
        """
        audience_styles = {
            "general": "in simple, accessible terms that anyone can understand",
            "technical": "with technical details, examples, and implementation considerations",
            "beginner": "step-by-step for someone completely new to the topic, with plenty of examples",
            "expert": "focusing on advanced aspects, nuances, and cutting-edge developments",
            "child": "using simple words, analogies, and fun examples that a child would understand",
        }

        style = audience_styles.get(audience, audience_styles["general"])
        return f"Please explain {concept} {style}. Include relevant examples and make it engaging for the {audience} audience."

    @mcp.prompt
    def debug_problem_prompt(problem_description: str, context: str = "") -> str:
        """Generate a prompt for debugging assistance.

        Args:
            problem_description: Description of the problem or error
            context: Additional context about the environment, code, or situation
        """
        context_section = f"\n\nContext:\n{context}" if context else ""

        return f"""I need help debugging the following problem:

**Problem Description:**
{problem_description}{context_section}

Please help me by:
1. Analyzing the potential root causes
2. Suggesting debugging steps or techniques
3. Providing possible solutions or workarounds
4. Recommending preventive measures for the future

If you need more information to provide better assistance, please let me know what additional details would be helpful."""

    @mcp.prompt
    def create_documentation_prompt(topic: str, doc_type: str = "user_guide") -> str:
        """Generate a prompt for creating documentation.

        Args:
            topic: Topic to document
            doc_type: Type of documentation (user_guide, api_reference, tutorial, troubleshooting)
        """
        doc_templates = {
            "user_guide": f"""Create a comprehensive user guide for {topic} that includes:
1. Overview and purpose
2. Getting started / quick start
3. Main features and functionality
4. Step-by-step instructions
5. Common use cases and examples
6. Tips and best practices
7. Troubleshooting common issues""",
            "api_reference": f"""Create detailed API reference documentation for {topic} including:
1. Overview and authentication
2. Base URLs and endpoints
3. Request/response formats
4. Parameters and data types
5. Error codes and handling
6. Rate limiting and quotas
7. Code examples in multiple languages
8. SDKs and libraries""",
            "tutorial": f"""Create a hands-on tutorial for {topic} that includes:
1. Learning objectives
2. Prerequisites and setup
3. Step-by-step walkthrough
4. Practical exercises
5. Code examples and explanations
6. Common pitfalls and solutions
7. Next steps and advanced topics""",
            "troubleshooting": f"""Create a troubleshooting guide for {topic} covering:
1. Common problems and symptoms
2. Diagnostic steps and tools
3. Solution procedures
4. Prevention strategies
5. When to seek additional help
6. Frequently asked questions
7. Known issues and workarounds""",
        }

        return doc_templates.get(doc_type, doc_templates["user_guide"])

    @mcp.prompt
    def optimize_performance_prompt(
        system_description: str, performance_goals: str = ""
    ) -> str:
        """Generate a prompt for performance optimization advice.

        Args:
            system_description: Description of the system or code to optimize
            performance_goals: Specific performance goals or constraints
        """
        goals_section = (
            f"\n\nPerformance Goals:\n{performance_goals}" if performance_goals else ""
        )

        return f"""I need help optimizing the performance of the following system:

**System Description:**
{system_description}{goals_section}

Please provide optimization recommendations covering:

1. **Performance Analysis**
   - Identify potential bottlenecks
   - Suggest profiling and monitoring approaches
   - Highlight critical performance metrics

2. **Optimization Strategies**
   - Algorithm and data structure improvements
   - Caching and memoization opportunities
   - Database query optimization
   - Network and I/O optimization

3. **Implementation Guidance**
   - Specific code changes or refactoring
   - Configuration adjustments
   - Infrastructure considerations

4. **Measurement and Validation**
   - How to measure improvements
   - Testing strategies
   - Performance regression prevention

Please prioritize recommendations based on potential impact and implementation effort."""

    @mcp.prompt
    def security_review_prompt(
        system_description: str, security_concerns: str = ""
    ) -> str:
        """Generate a prompt for security review and recommendations.

        Args:
            system_description: Description of the system to review
            security_concerns: Specific security concerns or requirements
        """
        concerns_section = (
            f"\n\nSpecific Concerns:\n{security_concerns}" if security_concerns else ""
        )

        return f"""Please conduct a security review of the following system:

**System Description:**
{system_description}{concerns_section}

Please analyze and provide recommendations for:

1. **Authentication & Authorization**
   - User authentication mechanisms
   - Access control and permissions
   - Session management

2. **Data Protection**
   - Data encryption (at rest and in transit)
   - Sensitive data handling
   - Privacy considerations

3. **Input Validation & Sanitization**
   - Input validation strategies
   - SQL injection prevention
   - XSS and CSRF protection

4. **Infrastructure Security**
   - Network security
   - Server hardening
   - Dependency management

5. **Monitoring & Incident Response**
   - Security logging and monitoring
   - Intrusion detection
   - Incident response procedures

Please prioritize recommendations based on risk level and provide specific implementation guidance."""
