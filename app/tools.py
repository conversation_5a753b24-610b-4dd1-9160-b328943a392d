"""FastMCP tools - Functions that LLMs can call to perform actions."""

import asyncio
import logging
from datetime import datetime
from typing import Any

from fastmcp import Context

logger = logging.getLogger(__name__)


def register_tools(mcp: Any) -> None:
    """Register all tools with the FastMCP server instance."""

    @mcp.tool
    def add_numbers(a: float, b: float) -> float:
        """Add two numbers together.

        Args:
            a: First number
            b: Second number

        Returns:
            The sum of a and b
        """
        logger.info(f"Adding {a} + {b}")
        return a + b

    @mcp.tool
    def multiply_numbers(a: float, b: float) -> float:
        """Multiply two numbers together.

        Args:
            a: First number
            b: Second number

        Returns:
            The product of a and b
        """
        logger.info(f"Multiplying {a} * {b}")
        return a * b

    @mcp.tool
    def calculate_power(base: float, exponent: float) -> float:
        """Calculate base raised to the power of exponent.

        Args:
            base: Base number
            exponent: Exponent

        Returns:
            base^exponent
        """
        logger.info(f"Calculating {base}^{exponent}")
        return base**exponent

    @mcp.tool
    async def get_current_time(timezone: str = "UTC") -> str:
        """Get the current time in the specified timezone.

        Args:
            timezone: Timezone name (e.g., 'UTC', 'US/Eastern', 'Europe/London')

        Returns:
            Current time as a formatted string
        """
        # This is a simple example - in a real implementation you'd use proper timezone handling
        current_time = datetime.now()
        return (
            f"Current time ({timezone}): {current_time.strftime('%Y-%m-%d %H:%M:%S')}"
        )

    @mcp.tool
    async def process_data_with_context(data: str, ctx: Context) -> str:
        """Process data with access to MCP context for logging and LLM sampling.

        Args:
            data: Data to process
            ctx: MCP context (automatically injected)

        Returns:
            Processed data summary
        """
        # Log to the client
        await ctx.info(f"Processing data: {data[:50]}...")

        # Simulate processing
        await asyncio.sleep(0.1)

        # Use LLM sampling to generate a summary (if client supports it)
        try:
            summary_prompt = f"Summarize this data in one sentence: {data}"
            summary_response = await ctx.sample(summary_prompt)
            # Handle different content types safely
            if summary_response and hasattr(summary_response, "content"):
                content = summary_response.content
                if hasattr(content, "text"):
                    summary = content.text
                else:
                    summary = str(content)
            else:
                summary = "Summary not available"
        except Exception as e:
            await ctx.warning(f"Could not generate summary: {e}")
            summary = f"Processed {len(data)} characters of data"

        await ctx.info("Processing completed")
        return summary

    @mcp.tool
    def format_text(text: str, format_type: str = "uppercase") -> str:
        """Format text in various ways.

        Args:
            text: Text to format
            format_type: Type of formatting (uppercase, lowercase, title, reverse)

        Returns:
            Formatted text
        """
        logger.info(f"Formatting text with {format_type}")

        if format_type == "uppercase":
            return text.upper()
        elif format_type == "lowercase":
            return text.lower()
        elif format_type == "title":
            return text.title()
        elif format_type == "reverse":
            return text[::-1]
        else:
            return text

    @mcp.tool
    async def simulate_api_call(
        endpoint: str, method: str = "GET", ctx: Context | None = None
    ) -> dict[str, Any]:
        """Simulate an API call to demonstrate async operations and context usage.

        Args:
            endpoint: API endpoint to call
            method: HTTP method (GET, POST, PUT, DELETE)
            ctx: MCP context (automatically injected)

        Returns:
            Simulated API response
        """
        if ctx:
            await ctx.info(f"Making {method} request to {endpoint}")

        # Simulate network delay
        await asyncio.sleep(0.5)

        # Simulate response
        response = {
            "status": "success",
            "endpoint": endpoint,
            "method": method,
            "timestamp": datetime.now().isoformat(),
            "data": {"message": "This is a simulated API response"},
        }

        if ctx:
            await ctx.info("API call completed successfully")

        return response
