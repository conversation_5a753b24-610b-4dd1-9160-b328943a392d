"""FastMCP resources - Read-only data sources that LLMs can access."""

import json
import logging
from datetime import datetime
from typing import Any

# Resources return simple strings, FastMCP handles the wrapping
from .config import settings

logger = logging.getLogger(__name__)


def register_resources(mcp: Any) -> None:
    """Register all resources with the FastMCP server instance."""

    @mcp.resource("config://server")
    def get_server_config() -> str:
        """Get server configuration information."""
        config_info = {
            "name": settings.server_name,
            "version": settings.server_version,
            "transport": settings.transport,
            "debug": settings.debug,
            "log_level": settings.log_level,
        }
        return json.dumps(config_info, indent=2)

    @mcp.resource("data://sample/{data_type}")
    def get_sample_data(data_type: str) -> str:
        """Get sample data of the specified type.

        Args:
            data_type: Type of sample data (users, products, orders, analytics)
        """
        sample_data = {
            "users": [
                {
                    "id": 1,
                    "name": "<PERSON>",
                    "email": "<EMAIL>",
                    "role": "admin",
                },
                {
                    "id": 2,
                    "name": "<PERSON>",
                    "email": "<EMAIL>",
                    "role": "user",
                },
                {
                    "id": 3,
                    "name": "<PERSON> <PERSON>",
                    "email": "<EMAIL>",
                    "role": "user",
                },
            ],
            "products": [
                {"id": 1, "name": "Widget Pro", "price": 19.99, "category": "tools"},
                {
                    "id": 2,
                    "name": "Super Gadget",
                    "price": 29.99,
                    "category": "electronics",
                },
                {
                    "id": 3,
                    "name": "Magic Device",
                    "price": 49.99,
                    "category": "electronics",
                },
            ],
            "orders": [
                {"id": 1, "user_id": 1, "product_id": 1, "quantity": 2, "total": 39.98},
                {"id": 2, "user_id": 2, "product_id": 2, "quantity": 1, "total": 29.99},
                {"id": 3, "user_id": 3, "product_id": 3, "quantity": 1, "total": 49.99},
            ],
            "analytics": {
                "total_users": 3,
                "total_products": 3,
                "total_orders": 3,
                "revenue": 119.96,
                "top_category": "electronics",
            },
        }

        if data_type not in sample_data:
            raise ValueError(
                f"Unknown data type: {data_type}. Available: {list(sample_data.keys())}"
            )

        return json.dumps(sample_data[data_type], indent=2)

    @mcp.resource("status://health")
    def get_health_status() -> str:
        """Get server health status."""
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "uptime": "N/A",  # In a real app, you'd track actual uptime
            "version": settings.server_version,
            "transport": settings.transport,
            "debug_mode": settings.debug,
        }
        return json.dumps(health_data, indent=2)

    @mcp.resource("docs://api/{section}")
    def get_api_documentation(section: str) -> str:
        """Get API documentation for different sections.

        Args:
            section: Documentation section (overview, tools, resources, prompts)
        """
        docs = {
            "overview": """
# FastMCP Template Server API

This server provides a comprehensive example of FastMCP capabilities including:
- Mathematical tools (add, multiply, power calculations)
- Text processing tools
- Async operations with context
- Sample data resources
- Health monitoring
- Interactive prompts

## Available Transports
- STDIO: For local command-line usage
- HTTP: For web-based deployments (recommended)
- SSE: Legacy transport (deprecated)
            """.strip(),
            "tools": """
# Available Tools

## Mathematical Operations
- `add_numbers(a, b)`: Add two numbers
- `multiply_numbers(a, b)`: Multiply two numbers
- `calculate_power(base, exponent)`: Calculate base^exponent

## Text Processing
- `format_text(text, format_type)`: Format text (uppercase, lowercase, title, reverse)

## Utility Tools
- `get_current_time(timezone)`: Get current time in specified timezone
- `process_data_with_context(data)`: Process data with LLM sampling
- `simulate_api_call(endpoint, method)`: Simulate API calls
            """.strip(),
            "resources": """
# Available Resources

## Configuration
- `config://server`: Server configuration information

## Sample Data
- `data://sample/users`: Sample user data
- `data://sample/products`: Sample product data
- `data://sample/orders`: Sample order data
- `data://sample/analytics`: Sample analytics data

## Status
- `status://health`: Server health status

## Documentation
- `docs://api/{section}`: API documentation sections
            """.strip(),
            "prompts": """
# Available Prompts

## Data Analysis
- `analyze_data_prompt(data_description, analysis_type)`: Generate data analysis prompts

## Code Review
- `code_review_prompt(code, language)`: Generate code review prompts

## Education
- `explain_concept_prompt(concept, audience)`: Generate concept explanation prompts
            """.strip(),
        }

        if section not in docs:
            available_sections = list(docs.keys())
            return f"Unknown documentation section: {section}\nAvailable sections: {', '.join(available_sections)}"

        return docs[section]

    @mcp.resource("examples://code/{language}")
    def get_code_examples(language: str) -> str:
        """Get code examples for different programming languages.

        Args:
            language: Programming language (python, javascript, bash)
        """
        examples = {
            "python": """
# Python FastMCP Client Example
from fastmcp import Client

async def main():
    async with Client("app/main.py") as client:
        # Call a tool
        result = await client.call_tool("add_numbers", {"a": 5, "b": 3})
        print(f"Result: {result.text}")
        # Read a resource
        config = await client.read_resource("config://server")
        print(f"Config: {config.text}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
            """.strip(),
            "javascript": """
// JavaScript MCP Client Example (conceptual)
const client = new MCPClient("http://localhost:8000/mcp");

async function main() {
    await client.connect();
    // Call a tool
    const result = await client.callTool("add_numbers", {a: 5, b: 3});
    console.log("Result:", result.text);
    // Read a resource
    const config = await client.readResource("config://server");
    console.log("Config:", config.text);
    await client.disconnect();
}

main().catch(console.error);
            """.strip(),
            "bash": """
#!/bin/bash
# Bash script to run FastMCP server

# Development mode
echo "Starting FastMCP server in development mode..."
uv run mcp-dev

# Production mode
# uv run mcp-prod

# Custom configuration
# MCP_TRANSPORT=http MCP_PORT=9000 uv run mcp-server
            """.strip(),
        }

        if language not in examples:
            available_languages = list(examples.keys())
            return f"No examples available for {language}. Available: {', '.join(available_languages)}"

        return examples[language]
